# SaaS de Gestion des Dépenses

Une application moderne de gestion des dépenses avec catégories, sous-catégories et suivi des dépenses, construite avec Node.js/Express pour le backend et HTML/Tailwind CSS pour le frontend.

## Fonctionnalités

- Gestion des catégories de dépenses
- Gestion des sous-catégories
- Enregistrement et suivi des dépenses
- Interface utilisateur moderne et futuriste
- Architecture modulaire et scalable

## Stack Technologique

### Backend
- Node.js
- Express.js
- SQLite (base de données)
- RESTful API

### Frontend
- HTML5
- JavaScript (vanilla)
- Tailwind CSS
- Architecture modulaire

## Structure du Projet

Pour plus de détails sur la structure du projet, voir [STRUCTURE.md](docs/STRUCTURE.md).

## Prérequis

- Node.js (version 14 ou supérieure)
- npm (généralement inclus avec Node.js)

## Installation

1. <PERSON><PERSON><PERSON> le dép<PERSON> (une fois le projet créé) :
   ```bash
   git clone <url-du-depot>
   cd javascript_2_expenses
   ```

2. Installer les dépendances du backend :
   ```bash
   cd backend
   npm install
   cd ..
   ```

3. Installer les dépendances du frontend (si nécessaire) :
   ```bash
   # Actuellement, le frontend n'utilise pas de gestionnaire de paquets,
   # mais Tailwind CSS nécessite npm pour la compilation
   ```

## Configuration

1. Configuration du backend :
   - Le backend utilisera SQLite par défaut (pas de configuration supplémentaire requise)
   - Les variables d'environnement peuvent être définies dans un fichier `.env` dans le dossier [backend/](file:///c%3A/Users/<USER>/OneDrive/Bureau/Simple_1/javascript_2_expenses/backend)

2. Configuration du frontend :
   - Le frontend est statique et ne nécessite pas de configuration spéciale
   - Les appels API sont configurés pour pointer vers `http://localhost:3000/api` par défaut

## Démarrage

1. Démarrer le serveur backend :
   ```bash
   cd backend
   npm start
   ```
   Le serveur démarrera sur le port 3000 par défaut.

2. Servir le frontend :
   - Vous pouvez utiliser n'importe quel serveur statique pour servir les fichiers du dossier [frontend/](file:///c%3A/Users/<USER>/OneDrive/Bureau/Simple_1/javascript_2_expenses/frontend)
   - Par exemple, avec Python :
     ```bash
     cd frontend
     python -m http.server 8000
     ```
   - Ou avec Node.js (si `http-server` est installé globalement) :
     ```bash
     cd frontend
     npx http-server -p 8000
     ```

3. Accéder à l'application :
   Ouvrez votre navigateur et allez à `http://localhost:8000`

## API Endpoints

Une fois le serveur backend démarré, les endpoints suivants seront disponibles :

### Catégories
- `GET /api/categories` - Liste toutes les catégories
- `GET /api/categories/:id` - Récupère une catégorie par ID
- `POST /api/categories` - Crée une nouvelle catégorie
- `PUT /api/categories/:id` - Met à jour une catégorie
- `DELETE /api/categories/:id` - Supprime une catégorie

### Sous-catégories
- `GET /api/subcategories` - Liste toutes les sous-catégories
- `GET /api/subcategories/:id` - Récupère une sous-catégorie par ID
- `POST /api/subcategories` - Crée une nouvelle sous-catégorie
- `PUT /api/subcategories/:id` - Met à jour une sous-catégorie
- `DELETE /api/subcategories/:id` - Supprime une sous-catégorie

### Dépenses
- `GET /api/expenses` - Liste toutes les dépenses
- `GET /api/expenses/:id` - Récupère une dépense par ID
- `POST /api/expenses` - Crée une nouvelle dépense
- `PUT /api/expenses/:id` - Met à jour une dépense
- `DELETE /api/expenses/:id` - Supprime une dépense

## Développement

### Backend

Le backend est structuré comme suit :
- Le point d'entrée est [server.js](file:///c%3A/Users/<USER>/OneDrive/Bureau/Simple_1/javascript_2_expenses/backend/server.js)
- Les routes sont définies dans le dossier [routes/](file:///c%3A/Users/<USER>/OneDrive/Bureau/Simple_1/javascript_2_expenses/backend/routes)
- La logique métier est dans le dossier [controllers/](file:///c%3A/Users/<USER>/OneDrive/Bureau/Simple_1/javascript_2_expenses/backend/controllers)
- Les modèles de données sont dans le dossier [models/](file:///c%3A/Users/<USER>/OneDrive/Bureau/Simple_1/javascript_2_expenses/backend/models)

### Frontend

Le frontend est structuré comme suit :
- La page principale est [index.html](file:///c%3A/Users/<USER>/OneDrive/Bureau/Simple_1/javascript_2_expenses/frontend/index.html)
- Les styles sont gérés avec Tailwind CSS
- Le JavaScript est organisé par fonctionnalité dans le dossier [js/](file:///c%3A/Users/<USER>/OneDrive/Bureau/Simple_1/javascript_2_expenses/frontend/js)

## Plan de développement

Pour plus de détails sur le plan de développement, voir [DEVELOPMENT_PLAN.md](docs/DEVELOPMENT_PLAN.md).

## Contribution

Les contributions sont les bienvenues ! Veuillez suivre ces étapes :

1. Fork le projet
2. Créez une branche pour votre fonctionnalité (`git checkout -b feature/AmazingFeature`)
3. Commitez vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Poussez vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## Licence

Ce projet est sous licence MIT - voir le fichier [LICENSE](LICENSE) pour plus de détails.

## Contact

Pour toute question ou suggestion, veuillez ouvrir une issue sur le dépôt.