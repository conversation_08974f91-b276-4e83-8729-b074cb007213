# Structure du Projet - SaaS de Gestion des Dépenses

## Architecture générale

```
javascript_2_expenses/
├── backend/                    # Application backend Node.js/Express
│   ├── config/                # Configuration de l'application
│   ├── controllers/           # Contrôleurs pour gérer la logique métier
│   ├── middleware/            # Middleware personnalisé
│   ├── models/                # Modèles de données et accès à la base
│   ├── routes/                # Définition des routes API
│   ├── utils/                 # Fonctions utilitaires
│   ├── database/              # Fichiers de base de données SQLite
│   ├── server.js             # Point d'entrée de l'application
│   └── package.json          # Dépendances backend
│
├── frontend/                  # Application frontend
│   ├── css/                  # Fichiers de style
│   │   └── tailwind.css      # Styles Tailwind CSS
│   ├── js/                   # Scripts JavaScript
│   │   ├── components/       # Composants UI réutilisables
│   │   ├── pages/            # Logique spécifique aux pages
│   │   ├── services/         # Services d'API
│   │   └── utils/            # Fonctions utilitaires frontend
│   ├── assets/               # Ressources statiques
│   │   ├── images/           # Images et icônes
│   │   └── fonts/            # Polices personnalisées
│   ├── index.html            # Page d'accueil
│   ├── dashboard.html        # Tableau de bord
│   ├── categories.html       # Gestion des catégories
│   ├── expenses.html         # Gestion des dépenses
│   └── reports.html          # Page de rapports
│
├── docs/                     # Documentation du projet
│   ├── DEVELOPMENT_PLAN.md   # Plan de développement
│   └── STRUCTURE.md          # Structure du projet
│
├── README.md                 # Instructions d'installation et d'utilisation
├── .gitignore                # Fichiers ignorés par Git
└── package.json              # Scripts et dépendances racine
```

## Détails de l'architecture

### Backend (`backend/`)

Le backend est construit avec Node.js et Express.js, utilisant SQLite comme base de données. L'architecture suit le modèle MVC (Modèle-Vue-Contrôleur) avec des composants supplémentaires pour une meilleure organisation.

#### Dossiers backend :
- `config/` : Configuration de l'application, variables d'environnement
- `controllers/` : Logique métier pour chaque entité (catégories, sous-catégories, dépenses)
- `middleware/` : Fonctions intermédiaires pour l'authentification, la validation, etc.
- `models/` : Modèles de données et interactions avec la base SQLite
- `routes/` : Définition des endpoints API RESTful
- `utils/` : Fonctions utilitaires réutilisables
- `database/` : Fichiers de base de données et scripts d'initialisation

### Frontend (`frontend/`)

Le frontend utilise HTML5, JavaScript vanilla et Tailwind CSS pour créer une interface utilisateur moderne et réactive.

#### Dossiers frontend :
- `css/` : Styles personnalisés et fichier Tailwind compilé
- `js/` : Toute la logique JavaScript frontend
  - `components/` : Composants UI réutilisables (modales, formulaires, etc.)
  - `pages/` : Logique spécifique à chaque page
  - `services/` : Services pour communiquer avec l'API backend
  - `utils/` : Fonctions utilitaires frontend
- `assets/` : Ressources statiques comme les images et polices
- Fichiers HTML : Pages principales de l'application

### Documentation (`docs/`)

Contient tous les documents de conception et de développement.

## Principes d'architecture

1. **Séparation des préoccupations** : Backend et frontend sont complètement séparés
2. **Modularité** : Chaque fonctionnalité est organisée dans des modules distincts
3. **Scalabilité** : Structure conçue pour faciliter l'ajout de nouvelles fonctionnalités
4. **Maintenabilité** : Code bien organisé et documenté
5. **Séparation des environnements** : Configuration différente pour développement et production

## Conventions de nommage

- Fichiers : snake_case pour les fichiers, PascalCase pour les classes
- Variables : camelCase pour les variables et fonctions
- Constantes : UPPER_SNAKE_CASE pour les constantes
- Classes : PascalCase pour les noms de classes
- ID CSS : kebab-case pour les identifiants CSS

## Flux de données

1. L'utilisateur interagit avec l'interface frontend
2. Le frontend envoie des requêtes HTTP aux endpoints API du backend
3. Le backend traite les requêtes via les routes et contrôleurs
4. Les données sont récupérées/manipulées dans la base SQLite via les modèles
5. Le backend renvoie les réponses au frontend
6. Le frontend met à jour l'interface utilisateur avec les données reçues