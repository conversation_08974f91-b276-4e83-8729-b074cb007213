const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Create a new database connection
const dbPath = path.join(__dirname, '../database/expenses.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
  } else {
    console.log('Connected to the SQLite database.');
  }
});

// Initialize the database tables
const initializeDatabase = () => {
  db.serialize(() => {
    // Create categories table
    db.run(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create subcategories table
    db.run(`
      CREATE TABLE IF NOT EXISTS subcategories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
      )
    `);

    // Create expenses table
    db.run(`
      CREATE TABLE IF NOT EXISTS expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        subcategory_id INTEGER NOT NULL,
        amount DECIMAL(10, 2) NOT NULL,
        description TEXT,
        date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (subcategory_id) REFERENCES subcategories (id) ON DELETE CASCADE
      )
    `);

    // Insert sample data if tables are empty
    db.get("SELECT COUNT(*) as count FROM categories", (err, row) => {
      if (row.count === 0) {
        // Insert sample categories
        const categories = [
          { name: 'Alimentation', description: 'Courses, restaurants, cafés' },
          { name: 'Logement', description: 'Loyer, utilities, meubles' },
          { name: 'Transport', description: 'Essence, transports en commun' },
          { name: 'Divertissement', description: 'Cinéma, concerts, jeux' }
        ];
        
        const categoryStmt = db.prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        categories.forEach(category => {
          categoryStmt.run(category.name, category.description);
        });
        categoryStmt.finalize();
        
        // Insert sample subcategories
        const subcategories = [
          { category_id: 1, name: 'Épicerie', description: 'Courses quotidiennes' },
          { category_id: 1, name: 'Restaurants', description: 'Repas au restaurant' },
          { category_id: 2, name: 'Loyer', description: 'Paiement du loyer mensuel' },
          { category_id: 2, name: 'Électricité', description: 'Facture d\'électricité' },
          { category_id: 3, name: 'Carburant', description: 'Essence pour la voiture' },
          { category_id: 3, name: 'Transports en commun', description: 'Abonnement mensuel' },
          { category_id: 4, name: 'Cinéma', description: 'Billets de cinéma' },
          { category_id: 4, name: 'Jeux', description: 'Jeux vidéo, jeux de société' }
        ];
        
        const subcategoryStmt = db.prepare("INSERT INTO subcategories (category_id, name, description) VALUES (?, ?, ?)");
        subcategories.forEach(subcategory => {
          subcategoryStmt.run(subcategory.category_id, subcategory.name, subcategory.description);
        });
        subcategoryStmt.finalize();
        
        // Insert sample expenses
        const expenses = [
          { subcategory_id: 1, amount: 85.50, description: 'Courses Carrefour', date: '2025-09-15' },
          { subcategory_id: 1, amount: 42.30, description: 'Courses Monoprix', date: '2025-09-10' },
          { subcategory_id: 2, amount: 35.00, description: 'Dîner restaurant', date: '2025-09-12' },
          { subcategory_id: 3, amount: 950.00, description: 'Loyer septembre', date: '2025-09-01' },
          { subcategory_id: 4, amount: 85.75, description: 'Facture EDF', date: '2025-09-05' },
          { subcategory_id: 5, amount: 65.40, description: 'Essence', date: '2025-09-14' },
          { subcategory_id: 6, amount: 75.00, description: 'Abonnement mensuel', date: '2025-09-01' },
          { subcategory_id: 7, amount: 12.50, description: 'Cinéma UGC', date: '2025-09-08' },
          { subcategory_id: 8, amount: 49.99, description: 'Jeu vidéo', date: '2025-09-16' }
        ];
        
        const expenseStmt = db.prepare("INSERT INTO expenses (subcategory_id, amount, description, date) VALUES (?, ?, ?, ?)");
        expenses.forEach(expense => {
          expenseStmt.run(expense.subcategory_id, expense.amount, expense.description, expense.date);
        });
        expenseStmt.finalize();
        
        console.log('Sample data inserted into database.');
      }
    });

    console.log('Database tables initialized.');
  });
};

// Initialize the database
initializeDatabase();

module.exports = db;