const Category = require('../models/Category');

// Create a new category
exports.createCategory = (req, res) => {
  const { name, description } = req.body;
  
  if (!name) {
    return res.status(400).json({ error: 'Category name is required' });
  }
  
  Category.create(name, description, (err, category) => {
    if (err) {
      if (err.message && err.message.includes('UNIQUE constraint failed')) {
        return res.status(400).json({ error: 'Category with this name already exists' });
      }
      return res.status(500).json({ error: 'Failed to create category' });
    }
    res.status(201).json(category);
  });
};

// Get all categories
exports.getAllCategories = (req, res) => {
  Category.findAll((err, categories) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to retrieve categories' });
    }
    res.json(categories);
  });
};

// Get a category by ID
exports.getCategoryById = (req, res) => {
  const { id } = req.params;
  
  Category.findById(id, (err, category) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to retrieve category' });
    }
    if (!category) {
      return res.status(404).json({ error: 'Category not found' });
    }
    res.json(category);
  });
};

// Update a category
exports.updateCategory = (req, res) => {
  const { id } = req.params;
  const { name, description } = req.body;
  
  if (!name) {
    return res.status(400).json({ error: 'Category name is required' });
  }
  
  Category.update(id, name, description, (err, category) => {
    if (err) {
      if (err.message === 'Category not found') {
        return res.status(404).json({ error: 'Category not found' });
      }
      return res.status(500).json({ error: 'Failed to update category' });
    }
    res.json(category);
  });
};

// Delete a category
exports.deleteCategory = (req, res) => {
  const { id } = req.params;
  
  Category.delete(id, (err, result) => {
    if (err) {
      if (err.message === 'Category not found') {
        return res.status(404).json({ error: 'Category not found' });
      }
      return res.status(500).json({ error: 'Failed to delete category' });
    }
    res.json(result);
  });
};