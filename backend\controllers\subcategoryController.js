const Subcategory = require('../models/Subcategory');

// Create a new subcategory
exports.createSubcategory = (req, res) => {
  const { category_id, name, description } = req.body;
  
  if (!category_id || !name) {
    return res.status(400).json({ error: 'Category ID and subcategory name are required' });
  }
  
  Subcategory.create(category_id, name, description, (err, subcategory) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to create subcategory' });
    }
    res.status(201).json(subcategory);
  });
};

// Get all subcategories
exports.getAllSubcategories = (req, res) => {
  Subcategory.findAll((err, subcategories) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to retrieve subcategories' });
    }
    res.json(subcategories);
  });
};

// Get subcategories by category ID
exports.getSubcategoriesByCategory = (req, res) => {
  const { categoryId } = req.params;
  
  Subcategory.findByCategoryId(categoryId, (err, subcategories) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to retrieve subcategories' });
    }
    res.json(subcategories);
  });
};

// Get a subcategory by ID
exports.getSubcategoryById = (req, res) => {
  const { id } = req.params;
  
  Subcategory.findById(id, (err, subcategory) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to retrieve subcategory' });
    }
    if (!subcategory) {
      return res.status(404).json({ error: 'Subcategory not found' });
    }
    res.json(subcategory);
  });
};

// Update a subcategory
exports.updateSubcategory = (req, res) => {
  const { id } = req.params;
  const { category_id, name, description } = req.body;
  
  if (!category_id || !name) {
    return res.status(400).json({ error: 'Category ID and subcategory name are required' });
  }
  
  Subcategory.update(id, category_id, name, description, (err, subcategory) => {
    if (err) {
      if (err.message === 'Subcategory not found') {
        return res.status(404).json({ error: 'Subcategory not found' });
      }
      return res.status(500).json({ error: 'Failed to update subcategory' });
    }
    res.json(subcategory);
  });
};

// Delete a subcategory
exports.deleteSubcategory = (req, res) => {
  const { id } = req.params;
  
  Subcategory.delete(id, (err, result) => {
    if (err) {
      if (err.message === 'Subcategory not found') {
        return res.status(404).json({ error: 'Subcategory not found' });
      }
      return res.status(500).json({ error: 'Failed to delete subcategory' });
    }
    res.json(result);
  });
};