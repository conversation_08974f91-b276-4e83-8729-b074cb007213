const db = require('../config/db');

class Category {
  // Create a new category
  static create(name, description, callback) {
    const sql = `INSERT INTO categories (name, description) VALUES (?, ?)`;
    db.run(sql, [name, description], function(err) {
      if (err) {
        callback(err, null);
      } else {
        callback(null, { id: this.lastID, name, description });
      }
    });
  }

  // Find all categories with subcategory count
  static findAll(callback) {
    const sql = `
      SELECT c.*, COUNT(s.id) as subcategory_count
      FROM categories c
      LEFT JOIN subcategories s ON c.id = s.category_id
      GROUP BY c.id
      ORDER BY c.name
    `;
    db.all(sql, [], (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }

  // Find a category by ID
  static findById(id, callback) {
    const sql = `SELECT * FROM categories WHERE id = ?`;
    db.get(sql, [id], (err, row) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, row);
      }
    });
  }

  // Update a category
  static update(id, name, description, callback) {
    const sql = `UPDATE categories SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    db.run(sql, [name, description, id], function(err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Category not found'), null);
      } else {
        Category.findById(id, callback);
      }
    });
  }

  // Delete a category
  static delete(id, callback) {
    const sql = `DELETE FROM categories WHERE id = ?`;
    db.run(sql, [id], function(err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Category not found'), null);
      } else {
        callback(null, { message: 'Category deleted successfully' });
      }
    });
  }
}

module.exports = Category;