const db = require('../config/db');

class Expense {
  // Create a new expense
  static create(subcategory_id, amount, description, date, callback) {
    const sql = `INSERT INTO expenses (subcategory_id, amount, description, date) VALUES (?, ?, ?, ?)`;
    db.run(sql, [subcategory_id, amount, description, date], function(err) {
      if (err) {
        callback(err, null);
      } else {
        callback(null, { 
          id: this.lastID, 
          subcategory_id, 
          amount, 
          description, 
          date 
        });
      }
    });
  }

  // Find all expenses with optional filters
  static findAll(filters = {}, callback) {
    let sql = `
      SELECT e.*, s.name as subcategory_name, c.name as category_name
      FROM expenses e
      JOIN subcategories s ON e.subcategory_id = s.id
      JOIN categories c ON s.category_id = c.id
    `;
    
    const params = [];
    const whereConditions = [];
    
    // Add filters if provided
    if (filters.category_id) {
      whereConditions.push('c.id = ?');
      params.push(filters.category_id);
    }
    
    if (filters.subcategory_id) {
      whereConditions.push('s.id = ?');
      params.push(filters.subcategory_id);
    }
    
    if (filters.start_date) {
      whereConditions.push('e.date >= ?');
      params.push(filters.start_date);
    }
    
    if (filters.end_date) {
      whereConditions.push('e.date <= ?');
      params.push(filters.end_date);
    }
    
    if (whereConditions.length > 0) {
      sql += ' WHERE ' + whereConditions.join(' AND ');
    }
    
    sql += ' ORDER BY e.date DESC, e.created_at DESC';
    
    // Handle limit for dashboard recent expenses
    if (filters.limit) {
      sql += ' LIMIT ?';
      params.push(filters.limit);
    }
    
    db.all(sql, params, (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }

  // Find an expense by ID
  static findById(id, callback) {
    const sql = `
      SELECT e.*, s.name as subcategory_name, c.name as category_name
      FROM expenses e
      JOIN subcategories s ON e.subcategory_id = s.id
      JOIN categories c ON s.category_id = c.id
      WHERE e.id = ?
    `;
    db.get(sql, [id], (err, row) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, row);
      }
    });
  }

  // Update an expense
  static update(id, subcategory_id, amount, description, date, callback) {
    const sql = `UPDATE expenses SET subcategory_id = ?, amount = ?, description = ?, date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    db.run(sql, [subcategory_id, amount, description, date, id], function(err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Expense not found'), null);
      } else {
        Expense.findById(id, callback);
      }
    });
  }

  // Delete an expense
  static delete(id, callback) {
    const sql = `DELETE FROM expenses WHERE id = ?`;
    db.run(sql, [id], function(err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Expense not found'), null);
      } else {
        callback(null, { message: 'Expense deleted successfully' });
      }
    });
  }

  // Get expense summary by category
  static getSummaryByCategory(callback) {
    const sql = `
      SELECT 
        c.name as category_name,
        COUNT(e.id) as expense_count,
        SUM(e.amount) as total_amount
      FROM expenses e
      JOIN subcategories s ON e.subcategory_id = s.id
      JOIN categories c ON s.category_id = c.id
      GROUP BY c.id, c.name
      ORDER BY total_amount DESC
    `;
    db.all(sql, [], (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }
}

module.exports = Expense;