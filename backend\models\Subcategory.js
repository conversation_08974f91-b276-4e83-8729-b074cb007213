const db = require('../config/db');

class Subcategory {
  // Create a new subcategory
  static create(category_id, name, description, callback) {
    const sql = `INSERT INTO subcategories (category_id, name, description) VALUES (?, ?, ?)`;
    db.run(sql, [category_id, name, description], function(err) {
      if (err) {
        callback(err, null);
      } else {
        callback(null, { 
          id: this.lastID, 
          category_id, 
          name, 
          description 
        });
      }
    });
  }

  // Find all subcategories with category name and expense count
  static findAll(callback) {
    const sql = `
      SELECT s.*, c.name as category_name, COUNT(e.id) as expense_count
      FROM subcategories s
      JOIN categories c ON s.category_id = c.id
      LEFT JOIN expenses e ON s.id = e.subcategory_id
      GROUP BY s.id
      ORDER BY c.name, s.name
    `;
    db.all(sql, [], (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }

  // Find subcategories by category ID
  static findByCategoryId(category_id, callback) {
    const sql = `SELECT * FROM subcategories WHERE category_id = ? ORDER BY name`;
    db.all(sql, [category_id], (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }

  // Find a subcategory by ID with category name
  static findById(id, callback) {
    const sql = `
      SELECT s.*, c.name as category_name
      FROM subcategories s
      JOIN categories c ON s.category_id = c.id
      WHERE s.id = ?
    `;
    db.get(sql, [id], (err, row) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, row);
      }
    });
  }

  // Update a subcategory
  static update(id, category_id, name, description, callback) {
    const sql = `UPDATE subcategories SET category_id = ?, name = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    db.run(sql, [category_id, name, description, id], function(err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Subcategory not found'), null);
      } else {
        Subcategory.findById(id, callback);
      }
    });
  }

  // Delete a subcategory
  static delete(id, callback) {
    const sql = `DELETE FROM subcategories WHERE id = ?`;
    db.run(sql, [id], function(err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Subcategory not found'), null);
      } else {
        callback(null, { message: 'Subcategory deleted successfully' });
      }
    });
  }
}

module.exports = Subcategory;