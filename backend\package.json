{"name": "expenses-management-backend", "version": "1.0.0", "description": "Backend for the Expenses Management SaaS", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["expenses", "management", "saas", "nodejs", "express"], "author": "Expenses Management Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2"}}