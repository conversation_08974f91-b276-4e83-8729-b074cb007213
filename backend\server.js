const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Import routes
const categoryRoutes = require('./routes/categories');
const subcategoryRoutes = require('./routes/subcategories');
const expenseRoutes = require('./routes/expenses');

// Initialize the app
const app = express();
const PORT = process.env.PORT || 3000;
const frontendPath = path.join(__dirname, '../frontend');

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files from the frontend directory
app.use(express.static(frontendPath));

// Middleware to handle routes without .html extension
app.get(['/', '/categories', '/subcategories', '/expenses', '/reports'], (req, res, next) => {
  let filePath = '';
  
  // Determine the file path based on the route
  switch(req.path) {
    case '/':
      filePath = path.join(frontendPath, 'index.html');
      break;
    case '/categories':
      filePath = path.join(frontendPath, 'categories.html');
      break;
    case '/subcategories':
      filePath = path.join(frontendPath, 'subcategories.html');
      break;
    case '/expenses':
      filePath = path.join(frontendPath, 'expenses.html');
      break;
    case '/reports':
      filePath = path.join(frontendPath, 'reports.html');
      break;
    default:
      return next();
  }
  
  // Check if file exists and serve it
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // If file doesn't exist, continue to next middleware
      next();
    } else {
      // If file exists, serve it
      res.sendFile(filePath);
    }
  });
});

// Routes
app.use('/api/categories', categoryRoutes);
app.use('/api/subcategories', subcategoryRoutes);
app.use('/api/expenses', expenseRoutes);

// Default route
app.get('/', (req, res) => {
  res.send('Expenses Management API is running...');
});

// API route for the base
app.get('/api', (req, res) => {
  res.json({
    message: 'Welcome to the Expenses Management API',
    version: '1.0.0',
    endpoints: {
      categories: '/api/categories',
      subcategories: '/api/subcategories',
      expenses: '/api/expenses'
    }
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`API endpoints available at http://localhost:${PORT}/api`);
  console.log(`Frontend available at http://localhost:${PORT}`);
});

module.exports = app;