<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expenses Manager - Gestion Moderne des Dépenses</title>
    <link href="css/tailwind.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-gray-900 to-gray-800 text-white min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-wallet text-indigo-500 text-2xl mr-2"></i>
                        <span class="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-500">
                            Expenses Manager
                        </span>
                    </div>
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="/" class="bg-gray-900 text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-home mr-1"></i> Tableau de bord
                            </a>
                            <a href="/categories" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-tags mr-1"></i> Catégories
                            </a>
                            <a href="/subcategories" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-list mr-1"></i> Sous-catégories
                            </a>
                            <a href="/expenses" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-receipt mr-1"></i> Dépenses
                            </a>
                            <a href="/reports" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-chart-line mr-1"></i> Rapports
                            </a>
                        </div>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6">
                        <button class="bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none">
                            <i class="fas fa-moon"></i>
                        </button>
                        <div class="ml-3 relative">
                            <div>
                                <button class="max-w-xs flex items-center text-sm rounded-full focus:outline-none">
                                    <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Utilisateur&background=0D8ABC&color=fff" alt="">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="-mr-2 flex md:hidden">
                    <button class="bg-gray-800 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-indigo-300 to-purple-400 mb-4">
                Gestion Intelligente des Dépenses
            </h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Suivez, analysez et optimisez vos dépenses avec notre plateforme moderne et intuitive
            </p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div class="bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-xl shadow-lg p-6 transform transition hover:scale-105">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-indigo-500 bg-opacity-30 mr-4">
                        <i class="fas fa-wallet text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-indigo-200">Total Dépenses</p>
                        <p id="totalExpenses" class="text-2xl font-bold">€0.00</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-purple-600 to-purple-800 rounded-xl shadow-lg p-6 transform transition hover:scale-105">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-purple-500 bg-opacity-30 mr-4">
                        <i class="fas fa-tags text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-purple-200">Catégories</p>
                        <p id="categoryCount" class="text-2xl font-bold">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-cyan-600 to-cyan-800 rounded-xl shadow-lg p-6 transform transition hover:scale-105">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-cyan-500 bg-opacity-30 mr-4">
                        <i class="fas fa-receipt text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-cyan-200">Transactions</p>
                        <p id="transactionCount" class="text-2xl font-bold">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Placeholder -->
        <div class="bg-gray-800 rounded-xl shadow-lg p-6 mb-12">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">Analyse des Dépenses</h2>
                <div class="flex space-x-2">
                    <button id="monthView" class="px-3 py-1 text-sm bg-gray-700 rounded-lg">Mois</button>
                    <button id="quarterView" class="px-3 py-1 text-sm bg-indigo-600 rounded-lg">Trimestre</button>
                    <button id="yearView" class="px-3 py-1 text-sm bg-gray-700 rounded-lg">Année</button>
                </div>
            </div>
            <div class="h-64 flex items-center justify-center bg-gray-900 rounded-lg">
                <canvas id="dashboardChart"></canvas>
            </div>
        </div>

        <!-- Recent Expenses -->
        <div class="bg-gray-800 rounded-xl shadow-lg p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">Dépenses Récentes</h2>
                <button id="addExpenseBtn" class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg transition">
                    <i class="fas fa-plus mr-2"></i> Ajouter Dépense
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-700">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Catégorie</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Montant</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700" id="recentExpensesTableBody">
                        <!-- Recent expenses will be dynamically inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 border-t border-gray-700 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="md:flex md:items-center md:justify-between">
                <div class="flex justify-center md:justify-start">
                    <div class="flex items-center">
                        <i class="fas fa-wallet text-indigo-500 mr-2"></i>
                        <span class="text-lg font-bold">Expenses Manager</span>
                    </div>
                </div>
                <div class="mt-4 md:mt-0 md:order-1 flex justify-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-gray-300">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-gray-300">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-gray-300">
                        <i class="fab fa-linkedin"></i>
                    </a>
                </div>
            </div>
            <div class="mt-4 text-center md:text-left text-sm text-gray-400">
                <p>&copy; 2025 Expenses Manager. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>