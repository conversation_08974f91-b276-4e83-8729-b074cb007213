// Dashboard page JavaScript

// DOM Elements
const totalExpensesElement = document.getElementById('totalExpenses');
const categoryCountElement = document.getElementById('categoryCount');
const transactionCountElement = document.getElementById('transactionCount');
const addExpenseBtn = document.getElementById('addExpenseBtn');
const monthViewBtn = document.getElementById('monthView');
const quarterViewBtn = document.getElementById('quarterView');
const yearViewBtn = document.getElementById('yearView');
const dashboardChartCtx = document.getElementById('dashboardChart');
const recentExpensesTableBody = document.getElementById('recentExpensesTableBody');

// Chart instance
let dashboardChart = null;

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    loadDashboardData();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    if (addExpenseBtn) {
        addExpenseBtn.addEventListener('click', () => {
            window.location.href = '/expenses';
        });
    }
    
    if (monthViewBtn) {
        monthViewBtn.addEventListener('click', () => {
            updateChartPeriod('month');
        });
    }
    
    if (quarterViewBtn) {
        quarterViewBtn.addEventListener('click', () => {
            updateChartPeriod('quarter');
        });
    }
    
    if (yearViewBtn) {
        yearViewBtn.addEventListener('click', () => {
            updateChartPeriod('year');
        });
    }
}

// Load dashboard data
async function loadDashboardData() {
    try {
        // Load summary data
        const summary = await api.getExpenseSummary();
        updateSummaryCards(summary);
        
        // Load recent expenses
        const expenses = await api.getExpenses({ limit: 5 });
        renderRecentExpenses(expenses);
        
        // Initialize chart
        initializeChart();
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
        showNotification('Erreur lors du chargement des données du tableau de bord', 'error');
    }
}

// Update summary cards
function updateSummaryCards(summary) {
    if (!summary || summary.length === 0) {
        if (totalExpensesElement) totalExpensesElement.textContent = '€0.00';
        if (categoryCountElement) categoryCountElement.textContent = '0';
        if (transactionCountElement) transactionCountElement.textContent = '0';
        return;
    }
    
    // Calculate totals
    let totalExpenses = 0;
    let totalTransactions = 0;
    
    summary.forEach(category => {
        totalExpenses += parseFloat(category.total_amount) || 0;
        totalTransactions += parseInt(category.expense_count) || 0;
    });
    
    // Update DOM elements
    if (totalExpensesElement) {
        totalExpensesElement.textContent = formatCurrency(totalExpenses);
    }
    
    if (categoryCountElement) {
        categoryCountElement.textContent = summary.length;
    }
    
    if (transactionCountElement) {
        transactionCountElement.textContent = totalTransactions;
    }
}

// Render recent expenses
function renderRecentExpenses(expenses) {
    if (!recentExpensesTableBody) return;
    
    if (!expenses || expenses.length === 0) {
        recentExpensesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-4 text-center text-gray-400">
                    Aucune dépense récente
                </td>
            </tr>
        `;
        return;
    }
    
    recentExpensesTableBody.innerHTML = expenses.map(expense => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium">${expense.description || 'Sans description'}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-300">${expense.category_name || 'N/A'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                ${formatDate(expense.date)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <span class="text-red-400">-${formatCurrency(expense.amount)}</span>
            </td>
        </tr>
    `).join('');
}

// Initialize chart
function initializeChart() {
    if (!dashboardChartCtx) return;
    
    // Sample data for the chart
    const data = {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jui'],
        datasets: [{
            label: 'Dépenses',
            data: [1200, 1900, 1500, 1800, 2200, 1950],
            borderColor: 'rgb(99, 102, 241)',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };
    
    const config = {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9ca3af'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9ca3af'
                    }
                }
            }
        }
    };
    
    dashboardChart = new Chart(dashboardChartCtx, config);
}

// Update chart period
function updateChartPeriod(period) {
    // Update button styles
    if (monthViewBtn) monthViewBtn.className = 'px-3 py-1 text-sm bg-gray-700 rounded-lg';
    if (quarterViewBtn) quarterViewBtn.className = 'px-3 py-1 text-sm bg-gray-700 rounded-lg';
    if (yearViewBtn) yearViewBtn.className = 'px-3 py-1 text-sm bg-gray-700 rounded-lg';
    
    // Highlight selected period
    switch(period) {
        case 'month':
            if (monthViewBtn) monthViewBtn.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg';
            break;
        case 'quarter':
            if (quarterViewBtn) quarterViewBtn.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg';
            break;
        case 'year':
            if (yearViewBtn) yearViewBtn.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg';
            break;
    }
    
    // In a real app, we would fetch new data based on the period
    showNotification(`Affichage des données pour: ${period}`, 'info');
}