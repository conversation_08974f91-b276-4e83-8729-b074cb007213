// Reports page JavaScript

// DOM Elements
const reportPeriodSelect = document.getElementById('reportPeriod');
const exportReportBtn = document.getElementById('exportReport');
const totalExpensesElement = document.getElementById('totalExpenses');
const categoryCountElement = document.getElementById('categoryCount');
const transactionCountElement = document.getElementById('transactionCount');
const averageMonthlyElement = document.getElementById('averageMonthly');
const categoriesTableBody = document.getElementById('categoriesTableBody');
const categoryChartCtx = document.getElementById('categoryChart');
const timeChartCtx = document.getElementById('timeChart');

// Chart instances
let categoryChart = null;
let timeChart = null;

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    loadReportData();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    if (reportPeriodSelect) {
        reportPeriodSelect.addEventListener('change', loadReportData);
    }
    
    if (exportReportBtn) {
        exportReportBtn.addEventListener('click', exportReport);
    }
}

// Load report data
async function loadReportData() {
    try {
        const summary = await api.getExpenseSummary();
        console.log('Received summary data:', summary); // Log for debugging
        updateSummaryCards(summary);
        renderCategoriesTable(summary);
        initializeCharts(summary);
    } catch (error) {
        console.error('Failed to load report data:', error);
        showNotification('Erreur lors du chargement des données de rapport', 'error');
    }
}

// Update summary cards
function updateSummaryCards(summary) {
    if (!summary || summary.length === 0) {
        if (totalExpensesElement) totalExpensesElement.textContent = '€0.00';
        if (categoryCountElement) categoryCountElement.textContent = '0';
        if (transactionCountElement) transactionCountElement.textContent = '0';
        if (averageMonthlyElement) averageMonthlyElement.textContent = '€0.00';
        return;
    }
    
    // Calculate totals
    let totalExpenses = 0;
    let totalTransactions = 0;
    
    summary.forEach(category => {
        totalExpenses += parseFloat(category.total_amount) || 0;
        totalTransactions += parseInt(category.expense_count) || 0;
    });
    
    // Calculate average monthly (assuming data is for current year)
    const averageMonthly = totalExpenses / 12;
    
    // Update DOM elements
    if (totalExpensesElement) {
        totalExpensesElement.textContent = formatCurrency(totalExpenses);
    }
    
    if (categoryCountElement) {
        categoryCountElement.textContent = summary.length;
    }
    
    if (transactionCountElement) {
        transactionCountElement.textContent = totalTransactions;
    }
    
    if (averageMonthlyElement) {
        averageMonthlyElement.textContent = formatCurrency(averageMonthly);
    }
}

// Render categories table
function renderCategoriesTable(categories) {
    if (!categoriesTableBody) return;
    
    if (!categories || categories.length === 0) {
        categoriesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-4 text-center text-gray-400">
                    Aucune donnée disponible
                </td>
            </tr>
        `;
        return;
    }
    
    // Sort categories by total amount (descending)
    const sortedCategories = [...categories].sort((a, b) => 
        (parseFloat(b.total_amount) || 0) - (parseFloat(a.total_amount) || 0)
    );
    
    // Calculate total for percentage calculation
    const totalAmount = sortedCategories.reduce((sum, category) => 
        sum + (parseFloat(category.total_amount) || 0), 0
    );
    
    categoriesTableBody.innerHTML = sortedCategories.map((category, index) => {
        // Assign colors based on index
        const colors = ['indigo', 'purple', 'cyan', 'pink', 'red', 'yellow', 'green', 'blue'];
        const color = colors[index % colors.length];
        const icon = ['utensils', 'home', 'car', 'gamepad', 'heartbeat', 'tshirt', 'book', 'plane'][index % colors.length];
        
        const amount = parseFloat(category.total_amount) || 0;
        const count = parseInt(category.expense_count) || 0;
        const percentage = totalAmount > 0 ? (amount / totalAmount * 100).toFixed(1) : 0;
        
        return `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 bg-${color}-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium">${category.category_name}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    ${count}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    ${formatCurrency(amount)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-700 rounded-full h-2 mr-2">
                            <div class="bg-${color}-500 h-2 rounded-full" style="width: ${percentage}%"></div>
                        </div>
                        <span class="text-sm text-gray-300">${percentage}%</span>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Initialize charts
function initializeCharts(categories) {
    if (categoryChartCtx) {
        // Destroy existing chart if it exists
        if (categoryChart) {
            categoryChart.destroy();
        }
        
        // Prepare data for category chart
        const labels = categories.map(cat => cat.category_name);
        const data = categories.map(cat => parseFloat(cat.total_amount) || 0);
        const backgroundColors = [
            'rgba(99, 102, 241, 0.7)',
            'rgba(139, 92, 246, 0.7)',
            'rgba(6, 182, 212, 0.7)',
            'rgba(236, 72, 153, 0.7)',
            'rgba(239, 68, 68, 0.7)',
            'rgba(245, 158, 11, 0.7)',
            'rgba(16, 185, 129, 0.7)',
            'rgba(59, 130, 246, 0.7)'
        ];
        
        const categoryChartData = {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColors.slice(0, labels.length),
                borderWidth: 0
            }]
        };
        
        const categoryChartConfig = {
            type: 'doughnut',
            data: categoryChartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#f9fafb',
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        };
        
        categoryChart = new Chart(categoryChartCtx, categoryChartConfig);
    }
    
    if (timeChartCtx) {
        // Destroy existing chart if it exists
        if (timeChart) {
            timeChart.destroy();
        }
        
        // Sample data for time chart (in a real app, this would come from the API)
        const timeLabels = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jui', 'Juil', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];
        const timeData = [1200, 1900, 1500, 1800, 2200, 1950, 2100, 1850, 1700, 1600, 1400, 1300];
        
        const timeChartData = {
            labels: timeLabels,
            datasets: [{
                label: 'Dépenses mensuelles',
                data: timeData,
                borderColor: 'rgb(99, 102, 241)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                tension: 0.4,
                fill: true
            }]
        };
        
        const timeChartConfig = {
            type: 'line',
            data: timeChartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9ca3af'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9ca3af'
                        }
                    }
                }
            }
        };
        
        timeChart = new Chart(timeChartCtx, timeChartConfig);
    }
}

// Export report
function exportReport() {
    showNotification('Fonction d\'exportation à implémenter', 'info');
    // In a real app, this would generate and download a report file
}