// Subcategories page JavaScript

// DOM Elements
const addSubcategoryBtn = document.getElementById('addSubcategoryBtn');
const subcategoryModal = document.getElementById('subcategoryModal');
const closeModal = document.getElementById('closeModal');
const cancelModal = document.getElementById('cancelModal');
const subcategoryForm = document.getElementById('subcategoryForm');
const subcategoryIdInput = document.getElementById('subcategoryId');
const subcategoryNameInput = document.getElementById('subcategoryName');
const subcategoryCategorySelect = document.getElementById('subcategoryCategory');
const subcategoryDescriptionInput = document.getElementById('subcategoryDescription');
const modalTitle = document.getElementById('modalTitle');
const subcategoriesTableBody = document.getElementById('subcategoriesTableBody');

// State
let currentSubcategoryId = null;

// Event Listeners
if (addSubcategoryBtn) {
    addSubcategoryBtn.addEventListener('click', () => openSubcategoryModal());
}

if (closeModal) {
    closeModal.addEventListener('click', () => closeSubcategoryModal());
}

if (cancelModal) {
    cancelModal.addEventListener('click', () => closeSubcategoryModal());
}

if (subcategoryForm) {
    subcategoryForm.addEventListener('submit', handleSubcategoryFormSubmit);
}

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    loadCategories();
    loadSubcategories();
});

// Load categories for dropdown
async function loadCategories() {
    try {
        const categories = await api.getCategories();
        renderCategoryOptions(categories);
    } catch (error) {
        console.error('Failed to load categories:', error);
        showNotification('Erreur lors du chargement des catégories', 'error');
    }
}

// Render category options for subcategory form
function renderCategoryOptions(categories) {
    if (!subcategoryCategorySelect) return;
    
    subcategoryCategorySelect.innerHTML = `
        <option value="">Sélectionner une catégorie</option>
        ${categories.map(category => `
            <option value="${category.id}">${category.name}</option>
        `).join('')}
    `;
}

// Load subcategories from API
async function loadSubcategories() {
    try {
        const subcategories = await api.getSubcategories();
        renderSubcategories(subcategories);
    } catch (error) {
        console.error('Failed to load subcategories:', error);
        showNotification('Erreur lors du chargement des sous-catégories', 'error');
    }
}

// Render subcategories in table
function renderSubcategories(subcategories) {
    if (!subcategoriesTableBody) return;
    
    if (subcategories.length === 0) {
        subcategoriesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-4 text-center text-gray-400">
                    Aucune sous-catégorie trouvée
                </td>
            </tr>
        `;
        return;
    }
    
    subcategoriesTableBody.innerHTML = subcategories.map(subcategory => `
        <tr data-id="${subcategory.id}">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium">${subcategory.name}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                ${subcategory.category_name || 'N/A'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                ${subcategory.description || 'Aucune description'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
                <div class="flex space-x-2">
                    <button class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg btn-icon edit-subcategory" data-id="${subcategory.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded-lg btn-icon delete-subcategory" data-id="${subcategory.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // Add event listeners to edit buttons
    document.querySelectorAll('.edit-subcategory').forEach(button => {
        button.addEventListener('click', (e) => {
            const subcategoryId = e.currentTarget.getAttribute('data-id');
            editSubcategory(subcategoryId);
        });
    });
    
    // Add event listeners to delete buttons
    document.querySelectorAll('.delete-subcategory').forEach(button => {
        button.addEventListener('click', (e) => {
            const subcategoryId = e.currentTarget.getAttribute('data-id');
            deleteSubcategory(subcategoryId);
        });
    });
}

// Open subcategory modal
async function openSubcategoryModal(subcategory = null) {
    currentSubcategoryId = subcategory ? subcategory.id : null;
    
    if (subcategory) {
        modalTitle.textContent = 'Modifier Sous-catégorie';
        subcategoryNameInput.value = subcategory.name || '';
        subcategoryCategorySelect.value = subcategory.category_id || '';
        subcategoryDescriptionInput.value = subcategory.description || '';
    } else {
        modalTitle.textContent = 'Nouvelle Sous-catégorie';
        subcategoryForm.reset();
    }
    
    subcategoryModal.classList.remove('hidden');
}

// Close subcategory modal
function closeSubcategoryModal() {
    subcategoryModal.classList.add('hidden');
    currentSubcategoryId = null;
    subcategoryForm.reset();
}

// Handle subcategory form submission
async function handleSubcategoryFormSubmit(e) {
    e.preventDefault();
    
    const name = subcategoryNameInput.value.trim();
    const categoryId = subcategoryCategorySelect.value;
    const description = subcategoryDescriptionInput.value.trim();
    
    if (!name || !categoryId) {
        showNotification('Veuillez remplir tous les champs requis', 'error');
        return;
    }
    
    try {
        const subcategoryData = { name, category_id: categoryId, description };
        
        if (currentSubcategoryId) {
            // Update existing subcategory
            await api.updateSubcategory(currentSubcategoryId, subcategoryData);
            showNotification('Sous-catégorie mise à jour avec succès', 'success');
        } else {
            // Create new subcategory
            await api.createSubcategory(subcategoryData);
            showNotification('Sous-catégorie créée avec succès', 'success');
        }
        
        closeSubcategoryModal();
        loadSubcategories();
    } catch (error) {
        console.error('Failed to save subcategory:', error);
        showNotification('Erreur lors de l\'enregistrement de la sous-catégorie', 'error');
    }
}

// Edit subcategory
async function editSubcategory(subcategoryId) {
    try {
        const subcategory = await api.getSubcategoryById(subcategoryId);
        openSubcategoryModal(subcategory);
    } catch (error) {
        console.error('Failed to load subcategory:', error);
        showNotification('Erreur lors du chargement de la sous-catégorie', 'error');
    }
}

// Delete subcategory
async function deleteSubcategory(subcategoryId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette sous-catégorie ? Toutes les dépenses associées seront également supprimées.')) {
        return;
    }
    
    try {
        await api.deleteSubcategory(subcategoryId);
        showNotification('Sous-catégorie supprimée avec succès', 'success');
        loadSubcategories();
    } catch (error) {
        console.error('Failed to delete subcategory:', error);
        showNotification('Erreur lors de la suppression de la sous-catégorie', 'error');
    }
}